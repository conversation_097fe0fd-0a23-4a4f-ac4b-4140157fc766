// ignore_for_file: depend_on_referenced_packages

import 'dart:async';

import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_darwin/local_auth_darwin.dart';

class BiometricService {
  static final _auth = LocalAuthentication();
  static bool _isAuthenticating = false;

  static Future<bool> hasBiometrics() async {
    try {
      return await _auth.canCheckBiometrics;
    } on PlatformException catch (e) {
      printty(e.toString(), level: 'Bio check');
      return false;
    }
  }

  static Future<bool> isBiometricAvailable() async {
    try {
      final bool canCheckBiometrics = await _auth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        printty('Device does not support biometric authentication',
            level: 'BiometricService');
        return false;
      }

      final List<BiometricType> availableBiometrics =
          await _auth.getAvailableBiometrics();

      if (availableBiometrics.isEmpty) {
        printty('No biometric authentication methods are enrolled',
            level: 'BiometricService');
        return false;
      }

      printty('Available biometrics: $availableBiometrics',
          level: 'BiometricService');
      return true;
    } on PlatformException catch (e) {
      printty('Error checking biometric availability: ${e.code} - ${e.message}',
          level: 'BiometricService');
      return false;
    }
  }

  static Future<bool> authenticate() async {
    // First check if biometric authentication is available
    final isAvailable = await isBiometricAvailable();
    if (!isAvailable) {
      printty('Biometric authentication is not available on this device',
          level: 'BiometricService');
      return false;
    }

    try {
      final bool didAuthenticate = await _auth.authenticate(
        authMessages: const <AuthMessages>[
          AndroidAuthMessages(
            cancelButton: 'Cancel',
            signInTitle: 'Biometric Authentication',
            // biometricHint: 'Touch sensor',
          ),
          IOSAuthMessages(
            cancelButton: 'Cancel',
            // goToSettingsButton: 'Settings',
            // goToSettingsDescription:
            //     'Please set up your biometric authentication.',
          ),
        ],
        // options: const AuthenticationOptions(
        //     useErrorDialogs: true, stickyAuth: false, biometricOnly: true),
        localizedReason: 'Use fingerprint to authorize action',
      );

      printty('Biometric authentication result: $didAuthenticate',
          level: 'BiometricService');
      return didAuthenticate;
    } on PlatformException catch (e) {
      printty('Biometric error: ${e.code} - ${e.message}',
          level: 'BiometricService');

      // Handle specific error codes
      switch (e.code) {
        case 'NotAvailable':
          printty('Biometric authentication is not available',
              level: 'BiometricService');
          break;
        case 'NotEnrolled':
          printty('No biometric credentials are enrolled',
              level: 'BiometricService');
          break;
        case 'UserCancel':
          printty('User canceled biometric authentication',
              level: 'BiometricService');
          break;
        case 'PermanentlyLockedOut':
          printty('Biometric authentication is permanently locked out',
              level: 'BiometricService');
          break;
        case 'LockedOut':
          printty('Biometric authentication is temporarily locked out',
              level: 'BiometricService');
          break;
        case 'no_fragment_activity':
          printty('Fragment activity error - check MainActivity implementation',
              level: 'BiometricService');
          break;
        default:
          printty('Unknown biometric error: ${e.code}',
              level: 'BiometricService');
      }

      return false;
    } catch (e) {
      printty('Unexpected error during biometric authentication: $e',
          level: 'BiometricService');
      return false;
    }
  }

  /// Persistent biometric authentication that keeps the prompt active until:
  /// 1) success, 2) timeout, or 3) user/system cancel.
  /// Automatically retries transient failures without requiring additional taps.
  static Future<bool> authenticatePersistent({
    Duration timeout = const Duration(seconds: 20),
    Duration retryDelay = const Duration(milliseconds: 300),
  }) async {
    final isAvailable = await isBiometricAvailable();
    if (!isAvailable) return false;

    if (_isAuthenticating) {
      // Already authenticating; avoid re-entrancy.
      printty('Biometric auth already in progress', level: 'BiometricService');
      return false;
    }

    final deadline = DateTime.now().add(timeout);
    bool success = false;
    bool canceled = false;

    while (DateTime.now().isBefore(deadline) && !success && !canceled) {
      final remaining = deadline.difference(DateTime.now());
      Timer? timer;
      if (remaining > Duration.zero) {
        timer = Timer(remaining, () async {
          try {
            await _auth.stopAuthentication();
            printty('Biometric auth stopped due to timeout',
                level: 'BiometricService');
          } catch (e) {
            // Ignore stop errors
          }
        });
      }

      try {
        _isAuthenticating = true;
        final result = await _auth.authenticate(
          authMessages: const <AuthMessages>[
            AndroidAuthMessages(
              cancelButton: 'Cancel',
              signInTitle: 'Biometric Authentication',
              // biometricHint: 'Touch sensor',
            ),
            IOSAuthMessages(
              cancelButton: 'Cancel',
              // goToSettingsButton: 'Settings',
              // goToSettingsDescription:
              //     'Please set up your biometric authentication.',
            ),
          ],
          // options: const AuthenticationOptions(
          //   useErrorDialogs: true,
          //   stickyAuth: true,
          //   biometricOnly: true,
          //   // sensitiveTransaction: false, // optional
          // ),
          localizedReason: 'Use fingerprint to authorize action',
        );
        success = result;
      } on PlatformException catch (e) {
        final code = e.code ?? '';
        printty('Biometric error in persistent flow: $code - ${e.message}',
            level: 'BiometricService');
        switch (code) {
          case 'UserCancel':
          case 'CanceledByUser':
          case 'SystemCancel':
            canceled = true;
            break;
          case 'PermanentlyLockedOut':
          case 'LockedOut':
          case 'NotAvailable':
          case 'NotEnrolled':
            // Non-recoverable during this session
            canceled = true;
            break;
          case 'auth_in_progress':
          case 'AuthInProgress':
            // Another session is active; wait briefly and retry until deadline
            await Future.delayed(const Duration(milliseconds: 250));
            break;
          default:
            // Treat other errors as transient and retry within session window
            await Future.delayed(const Duration(milliseconds: 250));
        }
      } catch (e) {
        // Unexpected errors: try again while within deadline
        await Future.delayed(const Duration(milliseconds: 250));
      } finally {
        _isAuthenticating = false;
        timer?.cancel();
      }

      if (!success && !canceled && DateTime.now().isBefore(deadline)) {
        await Future.delayed(retryDelay);
      }
    }

    return success;
  }

  static Future<String> getBiometricStatus() async {
    try {
      final bool canCheckBiometrics = await _auth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        return 'Device does not support biometric authentication';
      }

      final List<BiometricType> availableBiometrics =
          await _auth.getAvailableBiometrics();

      if (availableBiometrics.isEmpty) {
        return 'No biometric authentication methods are enrolled';
      }

      return 'Biometric authentication is available: ${availableBiometrics.join(', ')}';
    } on PlatformException catch (e) {
      return 'Error checking biometric status: ${e.code} - ${e.message}';
    }
  }
}
