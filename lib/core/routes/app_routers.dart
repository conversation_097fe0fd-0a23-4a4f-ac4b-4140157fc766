import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/auth/confirmation_body.dart';
import 'package:korrency/ui/screens/screens.dart';

class AppRouters {
  static Route<dynamic> getRoute(RouteSettings settings) {
    // GlobalVar.activeRoute = settings.name;
    final args = settings.arguments;
    final requestedName = settings.name ?? RoutePath.splashScreen;

    // Guard: prevent automatic routing for AppsFlyer OneLink referral paths
    // Example incoming path: "/9BIc/ref/202020". We show splash and let
    // normal app launch flow proceed without redirection.
    if (requestedName.startsWith('/9BIc/') || requestedName.contains('9BIc')) {
      // Notify DeepLinkHandler that app was opened via deep link
      DeepLinkHandler.instance.setAppOpenedViaDeepLink(settings.name);

      // Store the deep link path for potential use
      _storeInterceptedDeepLink(settings.name);

      return TransitionUtils.buildTransition(
        const SplashScreen(),
        const RouteSettings(name: RoutePath.splashScreen),
      );
    }

    switch (settings.name) {
      case RoutePath.welcomeBackScreen:
        bool fromSplash =
            settings.arguments is bool ? settings.arguments as bool : true;
        return TransitionUtils.buildTransition(
          WelcomeBackScreen(fromSplash: fromSplash),
          settings,
        );

      case RoutePath.splashScreen:
        return TransitionUtils.buildTransition(
          const SplashScreen(),
          settings,
        );

      case RoutePath.introScreen:
        return TransitionUtils.buildTransition(
          const IntroScreen(),
          settings,
        );

      case RoutePath.updateAvailableScreen:
        return TransitionUtils.buildTransition(
          const UpdateAvailableScreen(),
          settings,
        );

      case RoutePath.korrencyWelcomeScreen:
        return TransitionUtils.buildTransition(
          const KorrencyWelcomeScreen(),
          settings,
        );

      case RoutePath.createFreshDeskTicketWebview:
        final arg = settings.arguments;
        if (arg is WebViewArg) {
          return TransitionUtils.buildTransition(
            CreateFreshdeskTicketWebview(arg: arg),
            settings,
          );
        }
        return errorScreen(settings);

      // Auth flow
      case RoutePath.createAcctScreen:
        return TransitionUtils.buildTransition(
          const CreateAccountScreen(),
          settings,
        );

      case RoutePath.verifyPhoneNumScreen:
        return TransitionUtils.buildTransition(
          const VerifyPhoneNumberScreen(),
          settings,
        );

      case RoutePath.emailAndPasswordScreen:
        return TransitionUtils.buildTransition(
          const EmailAndPasswordScreen(),
          settings,
        );

      case RoutePath.verifyEmailScreen:
        return TransitionUtils.buildTransition(
          const VerifyEmailAddress(),
          settings,
        );

      case RoutePath.createPasswordScreen:
        return TransitionUtils.buildTransition(
          const CreatePasswordScreen(),
          settings,
        );

      case RoutePath.referralCodeScreen:
        return TransitionUtils.buildTransition(
          const ReferralCodeScreen(),
          settings,
        );

      case RoutePath.kycWelcomeScreen:
        return TransitionUtils.buildTransition(
          const KYCWelcomeScreen(),
          settings,
        );

      case RoutePath.completeProfileScreen:
        return TransitionUtils.buildTransition(
          const CompleteProfileScreen(),
          settings,
        );

      case RoutePath.setupPinScreen:
        return TransitionUtils.buildTransition(
          const SetupPinScreen(),
          settings,
        );

      case RoutePath.confirmPinScreen:
        return TransitionUtils.buildTransition(
          const ConfirmPinScreen(),
          settings,
        );

      case RoutePath.biometricScreen:
        return TransitionUtils.buildTransition(
          const BiometricScreen(),
          settings,
        );

      case RoutePath.frequentRecipientCountryScreen:
        final arg =
            settings.arguments is bool ? settings.arguments as bool : false;
        return TransitionUtils.buildTransition(
          FrequentRecipientCountryScreen(fromHome: arg),
          settings,
        );
      case RoutePath.successScreen:
        final args = settings.arguments;
        if (args is ConfirmationArg) {
          return TransitionUtils.buildTransition(
            SuccessScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.successConfirmScreen:
        final args = settings.arguments;
        if (args is SuccessConfirmArg) {
          return TransitionUtils.buildTransition(
            SuccessConfirmScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Login flow
      case RoutePath.loginScreen:
        return TransitionUtils.buildTransition(
          const LoginScreen(),
          settings,
        );

      case RoutePath.forgotPasswordScreen:
        return TransitionUtils.buildTransition(
          const ForgotPasswordScreen(),
          settings,
        );

      case RoutePath.createNewPasswordScreen:
        return TransitionUtils.buildTransition(
          const CreateNewPasswordScreen(),
          settings,
        );

      case RoutePath.passwordResetScreen:
        return TransitionUtils.buildTransition(
          const PasswordResetScreen(),
          settings,
        );

      case RoutePath.notifcationScreen:
        return TransitionUtils.buildTransition(
          const NotificationScreen(),
          settings,
        );

      case RoutePath.preferredMehodScreen:
        return TransitionUtils.buildTransition(
          const PreferredMethodScreen(),
          settings,
        );

      case RoutePath.interacTransferScreen:
        return TransitionUtils.buildTransition(
          const InteracTransferScreen(),
          settings,
        );

      case RoutePath.anotherInteracEmailScreen:
        return TransitionUtils.buildTransition(
          const AnotherInteracEmailScreen(),
          settings,
        );

      case RoutePath.verifyInteracEmailScreen:
        final args = settings.arguments;
        if (args is String) {
          return TransitionUtils.buildTransition(
            VerifyInteracEmailScreen(email: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.accountDetailsScreen:
        return TransitionUtils.buildTransition(
          const NgnAccountDetailsScreen(),
          settings,
        );

      case RoutePath.eurAccountDetailsScreen:
        if (args is Wallet) {
          return TransitionUtils.buildTransition(
            EurAccountDetailsScreen(
              wallet: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.accountLimitScreen:
        final args = settings.arguments;
        if (args is Currency) {
          return TransitionUtils.buildTransition(
            AccounntLimitScreen(currency: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.shareByUsernameScreen:
        return TransitionUtils.buildTransition(
          const ShareByUsernameScreen(),
          settings,
        );

      case RoutePath.accountStatementScreen:
        return TransitionUtils.buildTransition(
          const AccountStatementScreen(),
          settings,
        );

      case RoutePath.convertCurrencyScreen:
        final convertArg = args as ConvertArg?;
        return TransitionUtils.buildTransition(
          ConvertCurrencyScreen(convertArg: convertArg),
          settings,
        );

      case RoutePath.convertReviewScreen:
        return TransitionUtils.buildTransition(
          const ConvertReviewScreen(),
          settings,
        );

      // Send money flow
      case RoutePath.sendMoneyScreen:
        final args = settings.arguments as SendMoneyArg?;
        return TransitionUtils.buildTransition(
          SendMoneyScreen(arg: args),
          settings,
        );

      case RoutePath.sendToNgnScreen:
        if (args is TransferMethodArg) {
          return TransitionUtils.buildTransition(
            SendBankTransferScreen(
              arg: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.sendToCadScreen:
        if (args is TransferMethodArg) {
          return TransitionUtils.buildTransition(
            SendToCadScreen(
              transferMethodArg: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.reviewScreen:
        if (args is SendMoneyReviewsArg) {
          return TransitionUtils.buildTransition(
            SendMoneyReviewScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.quickSendScreen:
        return TransitionUtils.buildTransition(
          const QuickSendScreen(),
          settings,
        );

      case RoutePath.sendMoneyMethodScreen:
        return TransitionUtils.buildTransition(
          const SendMoneyMethodScreen(),
          settings,
        );

      // case RoutePath.sendMoneyAuthorizeScreen:
      //   return TransitionUtils.buildTransition(
      //     const SendMoneyAuthorizeScreen(),
      //     settings,
      //   );

      case RoutePath.sendMoneyMobileMoneyScreen:
        if (args is TransferMethodArg) {
          return TransitionUtils.buildTransition(
            SendMoneyMobileMoneyScreen(
              arg: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.sendMoneyBeneficiaryListScreen:
        if (args is TransferMethodArg) {
          return TransitionUtils.buildTransition(
            SendMoneyBeneficiaryListScreen(
              transferMethodArg: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.sendToIbanScreen:
        if (args is TransferMethodArg) {
          return TransitionUtils.buildTransition(
            SendToIbanScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Rewards
      case RoutePath.referralScreen:
        final arg = settings.arguments as bool?;
        return TransitionUtils.buildTransition(
          ReferralScreen(fromRewards: arg ?? false),
          settings,
        );

      case RoutePath.inviteAndEarnScreen:
        return TransitionUtils.buildTransition(
          const InviteOthersAndEarnScreen(),
          settings,
        );

      // case RoutePath.earningsScreen:
      //   return TransitionUtils.buildTransition(
      //     const EarningsScreen(),
      //     settings,
      //   );

      case RoutePath.korrencyPointScreen:
        return TransitionUtils.buildTransition(
          const KorrencyPointScreen(),
          settings,
        );

      case RoutePath.exchangeRateScreen:
        return TransitionUtils.buildTransition(
          const ExchangeRateScreen(),
          settings,
        );

      case RoutePath.navigateExchangeOffers:
        return TransitionUtils.buildTransition(
          const NavigateExchangeOffers(),
          settings,
        );

      case RoutePath.sendToKorrencyUser:
        return TransitionUtils.buildTransition(
          const KorrencyUserScreen(),
          settings,
        );

      case RoutePath.transactionScreen:
        return TransitionUtils.buildTransition(
          const TransactionsScreen(),
          settings,
        );

      case RoutePath.transactionStatusScreen:
        final args = settings.arguments;
        if (args is ConfirmationArg) {
          return TransitionUtils.buildTransition(
            TransactionStatusScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.transactionDetailsScreen:
        if (settings.arguments is TransactionArg) {
          return TransitionUtils.buildTransition(
            TransactionDetailsScreen(
              transactionArg: settings.arguments as TransactionArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.transactionReceiptScreen:
        if (settings.arguments is ReceiptArg) {
          return TransitionUtils.buildTransition(
            TransactionReceiptScreen(
              receiptArg: settings.arguments as ReceiptArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.securityScreen:
        return TransitionUtils.buildTransition(
          const SecurityScreen(),
          settings,
        );

      case RoutePath.answerQuestionScreen:
        return TransitionUtils.buildTransition(
          const AnswerQuestionScreen(),
          settings,
        );

      case RoutePath.confirmYourPinScreen:
        return TransitionUtils.buildTransition(
          const ConfirmYourPinScreen(),
          settings,
        );

      case RoutePath.setupYourPinScreen:
        return TransitionUtils.buildTransition(
          const SetupYourPinScreen(),
          settings,
        );

      // Change info
      case RoutePath.newPasswordScreen:
        return TransitionUtils.buildTransition(
          const NewPasswordScreen(),
          settings,
        );

      case RoutePath.changePasswordScreen:
        return TransitionUtils.buildTransition(
          const ChangePasswordScreen(),
          settings,
        );

      // case RoutePath.changeEmailPhoneScreen:
      //   if (settings.arguments is ChangeInfoArg) {
      //     return TransitionUtils.buildTransition(
      //       ChangeEmailPhoneScreen(
      //         arg: settings.arguments as ChangeInfoArg,
      //       ),
      //       settings,
      //     );
      //   }
      //   return errorScreen(settings);

      case RoutePath.newEmailScreen:
        return TransitionUtils.buildTransition(
          const NewEmailScreen(),
          settings,
        );

      case RoutePath.changeConfirmationScreen:
        if (settings.arguments is ChangeConfirmationArg) {
          return TransitionUtils.buildTransition(
            ChangeConfirmationScreen(
              arg: settings.arguments as ChangeConfirmationArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      // case RoutePath.changeErrorScreen:
      //   return TransitionUtils.buildTransition(
      //     const ChangeErrorScreen(),
      //     settings,
      //   );

      case RoutePath.newPhoneNumberScreen:
        return TransitionUtils.buildTransition(
          const NewPhoneNumberScreen(),
          settings,
        );

      case RoutePath.changeInfoOtpScreen:
        if (settings.arguments is ChangeInfoArg) {
          return TransitionUtils.buildTransition(
            ChangeInfoOtpScreen(
              arg: settings.arguments as ChangeInfoArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.changeTransactionPinScreen:
        return TransitionUtils.buildTransition(
          const ChangeTransactionPinScreen(),
          settings,
        );

      case RoutePath.myDeviceScreen:
        return TransitionUtils.buildTransition(
          const MyDevicesScreen(),
          settings,
        );

      case RoutePath.verifyTrustedDeviceScreen:
        return TransitionUtils.buildTransition(
          const VerifyTrustedDevicePinScreen(),
          settings,
        );

      case RoutePath.twoFactorAuthScreen:
        return TransitionUtils.buildTransition(
          const TwoFactorAuthScreen(),
          settings,
        );

      case RoutePath.securityQuestionScreen:
        return TransitionUtils.buildTransition(
          const SecurityQuestionsScreen(),
          settings,
        );

      case RoutePath.aboutUsScreen:
        return TransitionUtils.buildTransition(
          const AboutUsScreen(),
          settings,
        );

      // Wallet
      case RoutePath.cadWalletDetailsScreen:
        if (args is Wallet) {
          return TransitionUtils.buildTransition(
            CadWalletDetailsScreen(
              wallet: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.ngnWalletDetailsScreen:
        if (args is Wallet) {
          return TransitionUtils.buildTransition(
            NgnWalletDetailsScreen(
              wallet: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.gbpWalletDetailsScreen:
        if (args is Wallet) {
          return TransitionUtils.buildTransition(
            GbpWalletDetailsScreen(
              wallet: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.otherWalletDetailsScreen:
        if (args is Wallet) {
          return TransitionUtils.buildTransition(
            OtherWalletDetailsScreen(
              wallet: args,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.preferenceScreen:
        return TransitionUtils.buildTransition(
          const PreferenceScreen(),
          settings,
        );

      case RoutePath.helpSupportScreen:
        return TransitionUtils.buildTransition(
          const HelpSupportScreen(),
          settings,
        );

      case RoutePath.helpVideoScreen:
        return TransitionUtils.buildTransition(
          const HelpVideoScreen(),
          settings,
        );

      case RoutePath.reactOutToUsScreen:
        return TransitionUtils.buildTransition(
          const ReachOutToUsScreen(),
          settings,
        );

      case RoutePath.videoPlayerScreen:
        if (settings.arguments is VideoPlayerArg) {
          return TransitionUtils.buildTransition(
            VideoPlayerScreen(
              args: settings.arguments as VideoPlayerArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      // Beneficiary flow
      case RoutePath.infoBeneficiaryScreen:
        return TransitionUtils.buildTransition(
          const InfoBeneficiaryScreen(),
          settings,
        );

      case RoutePath.selectCountryScreen:
        return TransitionUtils.buildTransition(
          const SelectCountryScreen(),
          settings,
        );

      case RoutePath.ibanTransferScreen:
        if (args is BeneficiaryPaymentArg) {
          return TransitionUtils.buildTransition(
            IbanTransferScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.bankTransferScreen:
        if (settings.arguments is BeneficiaryPaymentArg) {
          return TransitionUtils.buildTransition(
            BankTransferScreen(
              arg: settings.arguments as BeneficiaryPaymentArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.mobileMoneyScreen:
        if (settings.arguments is BeneficiaryPaymentArg) {
          return TransitionUtils.buildTransition(
            MobileMoneyScreen(
              arg: settings.arguments as BeneficiaryPaymentArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.newBeneficiaryScreen:
        if (settings.arguments is Currency) {
          return TransitionUtils.buildTransition(
            NewBeneficiaryScreen(
              currency: settings.arguments as Currency,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.beneficiaryScreen:
        var arg = settings.arguments as BeneficiaryArg?;
        return TransitionUtils.buildTransition(
          BeneficiaryScreen(arg: arg),
          settings,
        );

      case RoutePath.beneficiaryPreferredPaymentScreen:
        if (settings.arguments is Currency) {
          return TransitionUtils.buildTransition(
            BeneficiaryPreferredMethodScreen(
              currency: settings.arguments as Currency,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.accountSettingScreen:
        return TransitionUtils.buildTransition(
          const AccountSettingScreen(),
          settings,
        );

      case RoutePath.profileScreen:
        return TransitionUtils.buildTransition(
          const ProfileScreen(),
          settings,
        );

      case RoutePath.selectAvatarScreen:
        return TransitionUtils.buildTransition(
          const SelectAvatarScreen(),
          settings,
        );

      case RoutePath.previewAvatarScreen:
        if (settings.arguments is String) {
          return TransitionUtils.buildTransition(
            PreviewAvatarScreen(
              avatarString: settings.arguments as String,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.deactivateAccountScreen:
        return TransitionUtils.buildTransition(
          const DeactivateAccountScreen(),
          settings,
        );

      case RoutePath.deactivateAccountReasonScreen:
        return TransitionUtils.buildTransition(
          const DeactivateAccountReasonScreen(),
          settings,
        );

      case RoutePath.howOfferWorks:
        return TransitionUtils.buildTransition(
          const HowItWorksScreen(),
          settings,
        );

      case RoutePath.createOfferScreen:
        return TransitionUtils.buildTransition(
          const CreateOfferScreen(),
          settings,
        );

      case RoutePath.createBuyOfferScreen:
        if (settings.arguments is OfferTypeArg) {
          return TransitionUtils.buildTransition(
            CreateOfferPageviewScreen(
              args: settings.arguments as OfferTypeArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.reviewOfferScreen:
        if (settings.arguments is OfferTypeArg) {
          return TransitionUtils.buildTransition(
            ReviewOfferScreen(
              args: settings.arguments as OfferTypeArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.offerTransactionStatusScreen:
        return TransitionUtils.buildTransition(
          const OfferTransactionStatusScreen(),
          settings,
        );

      case RoutePath.offerDetailsScreen:
        if (settings.arguments is OfferDetailArg) {
          return TransitionUtils.buildTransition(
            OfferDetailsScreen(
              arg: settings.arguments as OfferDetailArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.myOfferDetailsScreen:
        if (settings.arguments is OfferDetailArg) {
          return TransitionUtils.buildTransition(
            MyOfferDetailsScreen(
              arg: settings.arguments as OfferDetailArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.p2ptransferScreen:
        if (settings.arguments is P2pTransferArg) {
          return TransitionUtils.buildTransition(
            P2PTransferScreen(
              arg: settings.arguments as P2pTransferArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.p2pReviewScreen:
        return TransitionUtils.buildTransition(
          const P2PReviewScreen(),
          settings,
        );

      case RoutePath.p2pPinAuthorizeScreen:
        return TransitionUtils.buildTransition(
          const P2pPinAuthorizeScreen(),
          settings,
        );

      case RoutePath.setRateAlertScreen:
        return TransitionUtils.buildTransition(
          const SetRateAlertScreen(),
          settings,
        );

      case RoutePath.setAlertScreen:
        return TransitionUtils.buildTransition(
          const SetAlertScreen(),
          settings,
        );

      case RoutePath.createAlertScreen:
        return TransitionUtils.buildTransition(
          const CreateAlertScreen(),
          settings,
        );

      case RoutePath.sortOffersScreen:
        return TransitionUtils.buildTransition(
          const SortOffersScreen(),
          settings,
        );

      case RoutePath.yourOfferScreen:
        return TransitionUtils.buildTransition(
          const YourOfferScreen(),
          settings,
        );

      case RoutePath.marketPlaceScreen:
        return TransitionUtils.buildTransition(
          const MarketPlaceScreen(),
          settings,
        );

      case RoutePath.dashboardNav:
        int index = settings.arguments is int ? settings.arguments as int : 0;
        return TransitionUtils.buildTransition(
          DashboardNav(index: index),
          settings,
        );

      default:
        return errorScreen(settings);
    }
  }

  static errorScreen(RouteSettings settings) {
    return TransitionUtils.buildTransition(
      ScreenNotFound(routeName: settings.name),
      settings,
    );
  }

  /// Store the intercepted deep link for potential debugging/analytics
  static void _storeInterceptedDeepLink(String? routeName) {
    if (routeName == null) return;

    try {
      // Log the intercepted deep link
      printty('📝 Storing intercepted deep link: $routeName');

      // Store in temporary storage for analytics or debugging
      // You can extend this to store in SharedPreferences if needed
      final timestamp = DateTime.now().toIso8601String();

      // Log analytics event
      AppsFlyerService.instance.logEvent('deep_link_intercepted', {
        'route_path': routeName,
        'timestamp': timestamp,
        'action': 'redirected_to_splash',
      });
    } catch (e) {
      printty('❌ Error storing intercepted deep link: $e');
    }
  }
}
