import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/my_app.dart';

// Listen to background messages
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  printty('Handling a background message ${message.messageId}');
  FirebasePushNotificationService.firebaseMessagingBackgroundHandler(message);
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize environment configuration
  await EnvConfig.initialize("staging");
  await AppInitService().init(_firebaseMessagingBackgroundHandler);
  runApp(const MyApp());
}
