// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:korrency/session_manager.dart';

import 'core/core.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Configure inactivity timeout to 5 minutes
    SessionTimeoutService.instance.configure(
      inactivity: const Duration(hours: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return MultiProvider(
      providers: allProviders,
      child: ScreenUtilInit(
        designSize: const Size(390, 844),
        minTextAdapt: true,
        splitScreenMode: true,
        useInheritedMediaQuery: true,
        builder: (context, child) {
          return ActivityListener(
            child: MaterialApp(
              title: 'Korrency',
              debugShowCheckedModeBanner: false,
              theme: ThemeData(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
                scaffoldBackgroundColor: AppColors.bgWhite,
                fontFamily: AppFont.outfit.family,
                useMaterial3: true,
              ),
              // Global tap-to-dismiss keyboard wrapper
              builder: (context, child) {
                final mq = MediaQuery.of(context);
                final clampedScale = mq.textScaleFactor.clamp(0.85, 1.15);
                return MediaQuery(
                  data: mq.copyWith(textScaleFactor: clampedScale),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                    child: child ?? const SizedBox.shrink(),
                  ),
                );
              },
              navigatorKey: NavigatorKeys.appNavigatorKey,
              // Prevent automatic routing to unknown routes
              initialRoute: RoutePath.splashScreen,
              // Use our custom route generator that handles deep link interception
              onGenerateRoute: AppRouters.getRoute,
              // Handle unknown routes gracefully
              onUnknownRoute: (RouteSettings settings) {
                printty('⚠️ Unknown route detected: ${settings.name}');
                // Always redirect unknown routes to splash screen
                // Deep links will be handled by AppsFlyer callbacks
                return AppRouters.getRoute(RouteSettings(
                  name: RoutePath.splashScreen,
                  arguments: settings.arguments,
                ));
              },
            ),
          );
        },
      ),
    );
  }
}
