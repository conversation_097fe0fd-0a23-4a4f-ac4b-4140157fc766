import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _colorAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Color?> _colorAnimation;

  bool _showAnimatedLogo = false;
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final configVM = context.read<ConfigVM>();
      getPackageInfo();
      determinePosition();

      // Initialize FCM token service and set up refresh listener
      final fcmTokenService = FCMTokenService();
      fcmTokenService.getToken();
      fcmTokenService.setupTokenRefreshListener();

      // HeaderService().getDeviceInfo();

      context.read<AuthUserVM>().getUserFromStorage();
      configVM.getConfigurations().then((value) {
        printty(
            'Configurations BE: ${configVM.configData?.androidAppVersion}'); // Both for IOS and Android
        printty('Configurations Splash: ${configVM.appIsDueForUpdate}');
      });
    });

    _startLogoAnimation();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _colorAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _colorAnimation = ColorTween(
      begin: AppColors.white,
      end: AppColors.blue5B,
    ).animate(_colorAnimationController);

    _animationController.forward();
    _colorAnimationController.forward();

    Future.delayed(const Duration(seconds: 4), () {
      if (!mounted) return;

      if (context.read<ConfigVM>().appIsDueForUpdate) {
        Navigator.pushNamed(context, RoutePath.updateAvailableScreen);
        return;
      }

      // Check if app was opened via deep link
      if (DeepLinkHandler.instance.wasOpenedViaDeepLink) {
        printty(
            '🔗 App opened via deep link, waiting for AppsFlyer processing...');

        // Wait a bit longer for AppsFlyer to process the deep link
        Future.delayed(const Duration(seconds: 2), () {
          if (!mounted) return;

          // Clear the deep link flag
          DeepLinkHandler.instance.clearDeepLinkFlags();

          // If AppsFlyer hasn't handled the navigation yet, proceed normally
          _navigateBasedOnUserState();
        });
        return;
      }

      // Normal flow - navigate based on user state
      _navigateBasedOnUserState();
    });
  }

  void _navigateBasedOnUserState() {
    if (!mounted) return;

    Navigator.of(context).pushReplacementNamed(
      context.read<AuthUserVM>().user != null
          ? RoutePath.welcomeBackScreen
          : RoutePath.introScreen,
    );
  }

  getPackageInfo() {
    AppInitService().packageInfoInit().then((value) {
      if (value.isNotEmpty && mounted) {
        _appVersion = value;
        context.read<ConfigVM>().setMyAppCurrentVersion(value);
        setState(() {});
      }
    });
  }

  _startLogoAnimation() {
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _showAnimatedLogo = true;
      });
    });
  }

  @override
  void deactivate() {
    _animationController.dispose();
    _colorAnimationController.dispose();
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _colorAnimation,
      builder: (context, child) {
        return Scaffold(
          backgroundColor: AppColors.blue5B,
          body: Center(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // if (_showAnimatedLogo)
                        SvgPicture.asset(
                          AppSvgs.logoWhiteTwo,
                          height: Sizer.height(50),
                        ),
                        // if (!_showAnimatedLogo)
                        //   SvgPicture.asset(AppSvgs.korrencyBlack),
                      ],
                    ),
                  ),
                  // Align(
                  //   alignment: Alignment.center,
                  //   child: Text(
                  //     'Version $_appVersion',
                  //     style: AppTypography.text14.copyWith(
                  //       color: AppColors.grayDO,
                  //       fontWeight: FontWeight.w500,
                  //     ),
                  //   ),
                  // ),
                  const YBox(20),
                  CupertinoActivityIndicator(color: AppColors.grayDO),
                  const YBox(60),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
