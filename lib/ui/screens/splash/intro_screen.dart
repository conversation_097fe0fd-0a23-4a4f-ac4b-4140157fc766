import 'package:card_swiper/card_swiper.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        left: false,
        right: false,
        bottom: false,
        top: false,
        child: SizedBox(
          width: Sizer.screenWidth,
          height: Sizer.screenHeight,
          child: Column(
            children: [
              const YBox(20),
              Expanded(
                child: Swiper(
                  autoplay: true,
                  autoplayDisableOnInteraction: true,
                  autoplayDelay: 5000,
                  duration: 1000,
                  itemBuilder: (BuildContext context, int i) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        const YBox(120),
                        <PERSON><PERSON><PERSON><PERSON>(
                          height: 300,
                          child: imageHelper(OnboardCred.onboardCreds[i].image),
                        ),
                      ],
                    );
                  },
                  itemCount: OnboardCred.onboardCreds.length,
                  onIndexChanged: (index) {
                    onIndexChanged(index);
                  },
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ),
                child: Column(
                  children: [
                    Text(
                      OnboardCred.onboardCreds[currentIndex].title,
                      style: AppTypography.text26.semiBold.copyWith(
                        fontFamily: AppFont.outfit.family,
                        color: AppColors.black900,
                      ),
                    ),
                    const YBox(8),
                    Text(
                      OnboardCred.onboardCreds[currentIndex].desc,
                      textAlign: TextAlign.center,
                      style: AppTypography.text16.copyWith(
                        fontFamily: AppFont.outfit.family,
                        color: AppColors.grayAB,
                      ),
                    ),
                    const YBox(35),
                    CustomSwiperIndicator(
                      itemCount: OnboardCred.onboardCreds.length,
                      currentIndex: currentIndex,
                      primaryColor: AppColors.primaryBlue90,
                      dotSize: Sizer.radius(6),
                      activeDotSize: Sizer.radius(6),
                      spacing: Sizer.width(12),
                      animationDuration: Duration(milliseconds: 300),
                    ),
                    const YBox(50),
                    CustomBtn.solid(
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        Navigator.of(context)
                            .pushNamed(RoutePath.createAcctScreen);
                      },
                      online: true,
                      text: "Get Started",
                    ),
                    const YBox(16),
                    CustomBtn.solid(
                      isOutline: true,
                      textColor: AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        Navigator.of(context).pushNamed(RoutePath.loginScreen);
                      },
                      text: "Sign in",
                    ),
                    const YBox(30),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  onIndexChanged(int index) {
    if (currentIndex <= 3) {
      setState(() {
        currentIndex = index;
      });
    } else {
      currentIndex = 0;
    }
  }
}
