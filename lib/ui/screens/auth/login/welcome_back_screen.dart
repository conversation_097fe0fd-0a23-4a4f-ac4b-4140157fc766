// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';

class WelcomeBackScreen extends StatefulWidget {
  const WelcomeBackScreen({
    super.key,
    this.fromSplash = true,
  });

  final bool fromSplash;

  @override
  State<WelcomeBackScreen> createState() => _WelcomeBackScreenState();
}

class _WelcomeBackScreenState extends State<WelcomeBackScreen> {
  final _passwordC = TextEditingController();
  bool _bioInProgress = false;
  bool _fingerPromptShown = false;

  @override
  void initState() {
    super.initState();
    // Stop session timeout immediately when WelcomeBackScreen appears
    SessionTimeoutService.instance.stop();
    // Defer biometric prompt to after first frame to safely use context
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.read<AuthUserVM>().user != null && widget.fromSplash) {
        _biometricAuthentication();
      }
    });
  }

  @override
  void dispose() {
    _passwordC.dispose();
    super.dispose();
  }

  bool get btnIsActive => _passwordC.text.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Consumer<AuthUserVM>(builder: (context, vm, _) {
        return BusyOverlay(
          show: context.watch<LoginVM>().isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(10)),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const AuthHeader(
                              // showBackBtn: true,
                              ),
                          const YBox(40),
                          AuthTextSubTitle(
                            title: "Welcome Back, ${vm.user?.firstName ?? ""}",
                            subtitle:
                                "Enter your password or log in using biometrics",
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Password",
                            controller: _passwordC,
                            showLabelHeader: true,
                            isPassword: true,
                            borderRadius: Sizer.height(12),
                            labelSize: 14,
                            prefixIcon: Padding(
                              padding: EdgeInsets.all(Sizer.radius(12)),
                              child: SvgPicture.asset(
                                AppSvgs.passwordCheck,
                              ),
                            ),
                            onChanged: (_) => vm.reBuildUI(),
                            onSubmitted: (_) {
                              FocusScope.of(context).unfocus();
                            },
                          ),
                          const YBox(16),
                          const ForgotPasswordBtn(),
                          const YBox(30),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              RichText(
                                text: TextSpan(
                                  text: "Not you? ",
                                  style: AppTypography.text13.copyWith(
                                    color: AppColors.gray500,
                                    fontWeight: FontWeight.w400,
                                    height: 2,
                                  ),
                                  children: <InlineSpan>[
                                    WidgetSpan(
                                      child: InkWell(
                                        onTap: () {
                                          Navigator.pushNamed(
                                              context, RoutePath.loginScreen);
                                        },
                                        child: Text(
                                          "Switch Accounts",
                                          style: AppTypography.text14.copyWith(
                                            color: AppColors.blueE5,
                                            fontWeight: FontWeight.w500,
                                            height: 1.2,
                                            decoration:
                                                TextDecoration.underline,
                                            decorationColor: AppColors.blueE5,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          const YBox(80),
                          InkWell(
                            onTap: () => _biometricAuthentication(),
                            child: Container(
                              alignment: Alignment.center,
                              padding: const EdgeInsets.all(10),
                              child: SvgPicture.asset(
                                Platform.isIOS
                                    ? AppSvgs.faceId
                                    : AppSvgs.fingerprint,
                                height: Sizer.height(80),
                              ),
                            ),
                          ),
                          const YBox(40),
                          Align(
                            alignment: Alignment.center,
                            child: Text(
                              'Version ${context.read<ConfigVM>().myAppCurrentVersion ?? '1.0.0'}',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.grayE0,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const YBox(20),
                        ],
                      ),
                      CustomBtn.withChild(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          _login();
                        },
                        online: btnIsActive,
                        borderRadius: BorderRadius.circular(Sizer.radius(20)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Continue',
                              style: AppTypography.text15.copyWith(
                                color: btnIsActive
                                    ? AppColors.white
                                    : AppColors.gray51,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const XBox(8),
                            SvgPicture.asset(
                              AppSvgs.arrowRight,
                              colorFilter: ColorFilter.mode(
                                  btnIsActive
                                      ? AppColors.white
                                      : AppColors.gray51,
                                  BlendMode.srcIn),
                            )
                          ],
                        ),
                      ),
                      const YBox(30),
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.createAcctScreen);
                        },
                        child: RichText(
                          text: TextSpan(
                            text: "Don’t have an account yet? ",
                            style: AppTypography.text14.copyWith(
                              color: AppColors.gray500,
                              fontWeight: FontWeight.w400,
                              height: 2,
                            ),
                            children: [
                              TextSpan(
                                text: "Create Account",
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.blueE5,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const YBox(30),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  _login() async {
    final user = context.read<AuthUserVM>().user;
    final res = await context.read<LoginVM>().login(
          email: user?.email ?? "",
          password: _passwordC.text.trim(),
        );

    handleApiResponse(
      response: res,
      onSuccess: () {
        printty("User id ${user?.id}");
        MixpanelService().identify(user?.id ?? "");
        SessionTimeoutService.instance.start();
        if (widget.fromSplash) {
          Navigator.pushNamed(context, RoutePath.dashboardNav);
          return;
        }
        Navigator.pop(context);
      },
    );
  }

  /// Handles the complete biometric flow:
  /// 1. Checks if user has previously enabled biometrics.
  /// 2. Prompts to enable if not.
  /// 3. Attempts authentication only when enabled.
  Future<void> _biometricAuthentication() async {
    if (_bioInProgress) return;
    bool isEnabled =
        await StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled) ??
            false;

    if (!isEnabled) {
      if (_fingerPromptShown) return;
      _fingerPromptShown = true;
      isEnabled = await _showFingerPrompt();
      if (!isEnabled) {
        _fingerPromptShown = false;
        return;
      }
    }

    await _authenticateWithBiometrics();
  }

  /// Authenticates the user with biometrics and navigates on success.
  Future<void> _authenticateWithBiometrics() async {
    final ctx = NavigatorKeys.appNavigatorKey.currentContext;
    if (!await BiometricService.isBiometricAvailable()) {
      _showToast("Biometric authentication is not available on this device");
      return;
    }

    _bioInProgress = true;
    final bool authenticated = await BiometricService.authenticatePersistent(
      timeout: const Duration(seconds: 20),
    );
    _bioInProgress = false;

    if (authenticated) {
      SessionTimeoutService.instance.start();
      if (widget.fromSplash) {
        Navigator.pushNamedAndRemoveUntil(
          ctx!,
          RoutePath.dashboardNav,
          (route) => false,
        );
      } else {
        Navigator.pop(ctx!);
      }
    }
  }

  Future<bool> _showFingerPrompt() async {
    printty("Hello World");
    final res = await BsWrapper.bottomSheet(
      context: context,
      widget: const EnableBiometricsModal(),
      // widget: const EnableFingerprintSheet(),
    );
    return res is bool ? res : false;
  }

  _showToast(String message) {
    FlushBarToast.fLSnackBar(
      message: message,
    );
  }
}
