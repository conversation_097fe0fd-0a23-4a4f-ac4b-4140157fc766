import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SuccessScreen extends StatelessWidget {
  const SuccessScreen({
    Key? key,
    required this.arg,
  }) : super(key: key);

  final ConfirmationArg arg;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
              .copyWith(top: Sizer.height(20)),
          child: Column(
            children: [
              const AuthHeader(),
              Expanded(
                child: ConfirmationBody(arg: arg),
              ),
              CustomBtn.solid(
                onTap: arg.onBtnTap ?? () {},
                height: 65,
                online: true,
                text: arg.buttonText,
              ),
              const YBox(80),
            ],
          ),
        ),
      ),
    );
  }
}
