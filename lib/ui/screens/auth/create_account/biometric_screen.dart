import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';

class BiometricScreen extends StatefulWidget {
  const BiometricScreen({super.key});

  @override
  State<BiometricScreen> createState() => _BiometricScreenState();
}

class _BiometricScreenState extends State<BiometricScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
              .copyWith(top: Sizer.height(20)),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const AuthHeader(
                      showBackBtn: true,
                    ),
                    const YBox(50),
                    const AuthTextSubTitle(
                      title: "Enable Biometrics",
                      subtitle:
                          "For faster and more secure log in, enable your Fingerprint/Face ID.",
                    ),
                    const YBox(24),
                    Expanded(
                      child: Center(
                        child: Image.asset(
                          AppImages.fingerPrint,
                          height: Sizer.height(126),
                          width: Sizer.width(113),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              CustomBtn.solid(
                onTap: () {
                  updateFingerPrint();
                },
                online: true,
                height: 65,
                text: "Enable Biometrics",
              ),
              const YBox(30),
              InkWell(
              onTap: () {
                  SessionTimeoutService.instance.start();
                  StorageService.removeStringItem(StorageKey.logoutCount);
                  Navigator.pushNamedAndRemoveUntil(
                      context, RoutePath.dashboardNav, (route) => false);
                },
                child: Text(
                  "Skip for Now",
                  style: AppTypography.text14.copyWith(
                    color: AppColors.gray500,
                    fontWeight: FontWeight.w400,
                    height: 2,
                  ),
                ),
              ),
              const YBox(39),
            ],
          ),
        ),
      ),
    );
  }

  updateFingerPrint() async {
    // Check if biometric authentication is available before attempting
    final isAvailable = await BiometricService.isBiometricAvailable();
    if (!isAvailable) {
      final status = await BiometricService.getBiometricStatus();
      FlushBarToast.fLSnackBar(
        message: status,
      );
      return;
    }

    final result = await BiometricService.authenticate();
    if (result) {
      StorageService.storeBoolItem(StorageKey.fingerPrintIsEnabled, result);
      Navigator.pushNamed(
        context,
        RoutePath.successScreen,
        arguments: ConfirmationArg(
            title: "Welcome to Korrency",
            buttonText: 'Home',
            subtitle: const ConfirmationSubtitleText(
              startText: "Congratulations,",
              endText: " your account setup was successful.",
            ),
            onBtnTap: () {
              SessionTimeoutService.instance.start();
              Navigator.pushReplacementNamed(
                context,
                RoutePath.dashboardNav,
              );
            }),
      );
    } else {
      // Don't show error for user cancellation
      final status = await BiometricService.getBiometricStatus();
      if (!status.contains('canceled') && !status.contains('UserCancel')) {
        FlushBarToast.fLSnackBar(
          message: "Biometric Authentication Failed",
        );
      }
    }
  }
}
