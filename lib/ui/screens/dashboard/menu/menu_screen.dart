import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;
  @override
  void initState() {
    super.initState();
    _customController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });
  }

  @override
  void dispose() {
    _customController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: Sizer.screenHeight,
        width: Sizer.screenWidth,
        color: AppColors.white,
        child: BusyOverlay(
          show: context.watch<LoginVM>().isBusy,
          child: Consumer<AuthUserVM>(builder: (context, vm, _) {
            return SafeArea(
              bottom: false,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      YBox(20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          UserAvatar(
                            avatarUrl: vm.user?.avatarUrl ?? "",
                            onTap: () {
                              printty(
                                  "UserAvatar tapped ${vm.user?.avatarUrl}");
                            },
                          ),
                          // InkWell(
                          //   onTap: () {
                          //     context.read<FreshChatVM>()
                          //       ..initializeFreshchat()
                          //       ..configureFreshchatUser(
                          //           context.read<AuthUserVM>().user);
                          //     Future.delayed(const Duration(seconds: 1), () {
                          //       Freshchat.showConversations();
                          //     });
                          //   },
                          //   child: Row(
                          //     children: [
                          //       SvgPicture.asset(
                          //         AppSvgs.headphone,
                          //         height: Sizer.height(20),
                          //         width: Sizer.width(20),
                          //       ),
                          //       const XBox(8),
                          //       Text(
                          //         "Support",
                          //         style: AppTypography.text14
                          //             .copyWith(color: AppColors.white),
                          //       )
                          //     ],
                          //   ),
                          // ),
                        ],
                      ),
                      const YBox(6),
                      Text(
                        vm.user?.fullName ?? "",
                        style: AppTypography.text18.copyWith(
                          fontFamily: AppFont.outfit.family,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      Text(
                        vm.user?.userName ?? "",
                        style: AppTypography.text16.copyWith(
                          fontFamily: AppFont.outfit.family,
                          color: AppColors.gray79,
                        ),
                      ),
                    ],
                  ),
                  YBox(10),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(24),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.inviteAndEarnScreen,
                        );
                      },
                      // child: imageHelper(AppImages.profileBadge),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(16),
                          vertical: Sizer.height(16),
                        ),
                        height: Sizer.height(80),
                        width: Sizer.screenWidth,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage(AppImages.inviteBadge),
                            // fit: BoxFit.cover,
                          ),
                        ),
                        child: Text(
                          "Invite your friends and both \nearn ${vm.referralRule?.currency.symbol}${vm.referralRule?.referredBonus ?? "0"} cashback",
                          style: AppTypography.text16.copyWith(
                            fontFamily: AppFont.inter.family,
                            fontWeight: FontWeight.w600,
                            color: AppColors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      width: Sizer.screenWidth,
                      color: AppColors.white,
                      child: FadeTransition(
                        opacity: _customAnimation,
                        child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0, 0.2),
                            end: const Offset(0, 0),
                          ).animate(_customAnimation),
                          child: ListView(
                            padding: EdgeInsets.only(
                              top: Sizer.height(30),
                              bottom: Sizer.height(24),
                              right: Sizer.width(24),
                              left: Sizer.width(24),
                            ),
                            children: [
                              MenuListTile(
                                title: 'Account Settings',
                                subTitle: 'Your personal details',
                                icon: AppSvgs.gearRepo,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.accountSettingScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: 'Profile Settings',
                                subTitle: 'Your personal details',
                                icon: AppSvgs.userProfile,
                                onPressed: () {
                                  Navigator.pushNamed(
                                      context, RoutePath.profileScreen);
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: 'Privacy and Security',
                                subTitle: 'Change password, email and more',
                                icon: AppSvgs.shield,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.securityScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: 'Preferences',
                                subTitle: 'Notifications, Dark mode and more',
                                icon: AppSvgs.preference,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.preferenceScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: 'Beneficiaries',
                                subTitle: 'Manage all your saved beneficiaries',
                                icon: AppSvgs.beneficiary,
                                onPressed: () {
                                  if (!vm.userIsVerified) {
                                    showWarningToast(
                                        'Complete your KYC to add beneficiaries');
                                    return;
                                  }
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.beneficiaryScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: 'About Korrency',
                                subTitle:
                                    'Your Korrency help tools, all in one spot',
                                icon: AppSvgs.about,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.aboutUsScreen,
                                  );
                                },
                              ),
                              const YBox(50),
                              MenuListTile(
                                title: 'Help and Support',
                                subTitle:
                                    'Fill in your details to get started.',
                                icon: AppSvgs.helpSupport,
                                onPressed: () {
                                  MixpanelService().track("Support Accessed");
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.helpSupportScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: 'Logout',
                                icon: AppSvgs.logout,
                                onPressed: () {
                                  BsWrapper.bottomSheet(
                                    context: context,
                                    widget: ConfirmationSheet(
                                      title: vm.user?.firstName ??
                                          vm.user?.userName,
                                      message:
                                          'Are you sure you want to log out of Korrency?',
                                      secondBtnText: "Log out",
                                      secendBtnTap: () => _handleLogout(),
                                    ),
                                  );
                                },
                              ),
                              // MenuListTileOLD(
                              //   title: 'Referrals',
                              //   iconData: Iconsax.gift,
                              //   onPressed: () {
                              //     // FlushBarToast.fLSnackBar(
                              //     //   message: 'Refer a friend is coming soon',
                              //     //   snackBarType: SnackBarType.success,
                              //     // );
                              //     BsWrapper.bottomSheet(
                              //       context: context,
                              //       widget: const InviteOthersAndEarnSheet(),
                              //     );
                              //   },
                              // ),
                              // const YBox(20),
                              // MenuListTileOLD(
                              //   title: 'About Korrency',
                              //   useImageIcon: true,
                              //   onPressed: () {
                              //     Navigator.pushNamed(
                              //       context,
                              //       RoutePath.aboutUsScreen,
                              //     );
                              //   },
                              // ),

                              const YBox(30),
                              InkWell(
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, RoutePath.updateAvailableScreen);
                                },
                                child: Align(
                                  alignment: Alignment.center,
                                  child: Text(
                                    'Version ${context.read<ConfigVM>().myAppCurrentVersion ?? '1.0.0'}',
                                    style: AppTypography.text14.copyWith(
                                      color: AppColors.grayE0,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                              const YBox(100),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
      ),
    );
  }

  void _handleLogout() async {
    final count = int.tryParse(
            await StorageService.getString(StorageKey.logoutCount) ?? "0") ??
        0;
    StorageService.storeString(StorageKey.logoutCount, '${count + 1}');
    navigate();
  }

  void navigate() {
    Navigator.pop(context);
    context.read<LoginVM>().logout();
  }
}
